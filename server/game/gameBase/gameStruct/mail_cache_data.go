package gameStruct

import (
	"context"
	"fmt"
	"strconv"
	"time"
	"world/common/pbBase/MailDefine"
	"world/db"
	ut "world/utils"
	"world/utils/serializer"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 可以合并的邮件类型
var CanMergeMailType = map[MailDefine.MAIL_TYPE]MailDefine.MAIL_TYPE{
	MailDefine.BACK:    MailDefine.RECEIPT,
	MailDefine.RECEIPT: MailDefine.BACK,
	MailDefine.SERVICE: MailDefine.SYSTEM,
	MailDefine.SYSTEM:  MailDefine.SERVICE,
}

var AllMailTypes = []MailDefine.MAIL_TYPE{
	MailDefine.SEND, MailDefine.SYSTEM, MailDefine.MONEY, MailDefine.TASK,
	MailDefine.SERVICE, MailDefine.PLAYER, MailDefine.BACK, MailDefine.RECEIPT, MailDefine.CHARGE_FAIL,
}

func NewMailCacheData(plr *Player) *MailCacheData {
	return &MailCacheData{plr: plr}
}

// 单个邮件类型的统计信息 - 支持所有邮件状态
type MailTypeStats struct {
	UnreadText  int `marshal:"-"` // 未读纯文本 (UNREAD_TEXT = 1)
	UnreadItem  int `marshal:"-"` // 未读有附件 (UNREAD_ITEM = 2)
	UnreadTrade int `marshal:"-"` // 未读交易 (UNREAD_TRADE = 3)
	ReadText    int `marshal:"-"` // 已读纯文本 (READ_TEXT = 4)
	ReadItem    int `marshal:"-"` // 已读有附件 (READ_ITEM = 5)
	ReadTrade   int `marshal:"-"` // 已读交易 (READ_TRADE = 6)
	ReadNoItem  int `marshal:"-"` // 已读无附件 (READ_NO_ITEM = 7)
}

// GetUnreadTotal 获取未读总数
func (s *MailTypeStats) GetUnreadTotal() int {
	return s.UnreadText + s.UnreadItem + s.UnreadTrade
}

// GetReadTotal 获取已读总数
func (s *MailTypeStats) GetReadTotal() int {
	return s.ReadText + s.ReadItem + s.ReadTrade + s.ReadNoItem
}

// GetTotal 获取总数
func (s *MailTypeStats) GetTotal() int {
	return s.GetUnreadTotal() + s.GetReadTotal()
}

// IsEmpty 检查是否为空
func (s *MailTypeStats) IsEmpty() bool {
	return s.GetTotal() == 0
}

// GetStatusCount 根据状态获取数量
func (s *MailTypeStats) GetStatusCount(status MailDefine.MAIL_STATUS) int {
	switch status {
	case MailDefine.UNREAD_TEXT:
		return s.UnreadText
	case MailDefine.UNREAD_ITEM:
		return s.UnreadItem
	case MailDefine.UNREAD_TRADE:
		return s.UnreadTrade
	case MailDefine.READ_TEXT:
		return s.ReadText
	case MailDefine.READ_ITEM:
		return s.ReadItem
	case MailDefine.READ_TRADE:
		return s.ReadTrade
	case MailDefine.READ_NO_ITEM:
		return s.ReadNoItem
	default:
		return 0
	}
}

// SetStatusCount 设置状态数量
func (s *MailTypeStats) SetStatusCount(status MailDefine.MAIL_STATUS, count int) {
	if count < 0 {
		count = 0
	}
	switch status {
	case MailDefine.UNREAD_TEXT:
		s.UnreadText = count
	case MailDefine.UNREAD_ITEM:
		s.UnreadItem = count
	case MailDefine.UNREAD_TRADE:
		s.UnreadTrade = count
	case MailDefine.READ_TEXT:
		s.ReadText = count
	case MailDefine.READ_ITEM:
		s.ReadItem = count
	case MailDefine.READ_TRADE:
		s.ReadTrade = count
	case MailDefine.READ_NO_ITEM:
		s.ReadNoItem = count
	}
}

// AddStatusCount 增加状态数量
func (s *MailTypeStats) AddStatusCount(status MailDefine.MAIL_STATUS, delta int) {
	current := s.GetStatusCount(status)
	s.SetStatusCount(status, current+delta)
}

func (s *MailTypeStats) MergeAsNew(from *MailTypeStats) *MailTypeStats {
	newStats := &MailTypeStats{}
	newStats.UnreadText = s.UnreadText + from.UnreadText
	newStats.UnreadItem = s.UnreadItem + from.UnreadItem
	newStats.UnreadTrade = s.UnreadTrade + from.UnreadTrade
	newStats.ReadText = s.ReadText + from.ReadText
	newStats.ReadItem = s.ReadItem + from.ReadItem
	newStats.ReadTrade = s.ReadTrade + from.ReadTrade
	newStats.ReadNoItem = s.ReadNoItem + from.ReadNoItem
	return newStats
}

type MailCacheData struct {
	plr           *Player                                 `marshal:"-"` // 玩家
	mailsCache    map[MailDefine.MAIL_TYPE]*MailTypeCache `marshal:"-"` // 各类型邮件的缓存信息（包含统计信息）
	deleteWaitAry []*Mail                                 `marshal:"-"` // 需要删除的邮件
}

// MailTypeCache 单个邮件类型的缓存信息（合并了统计信息）
type MailTypeCache struct {
	// 邮件缓存相关
	mails       []*Mail `marshal:"-"` // 该类型的邮件缓存
	lastId      string  `marshal:"-"` // 最后一条记录的_id
	lastPage    int     `marshal:"-"` // 最后缓存到的页码
	UnreadText  int     `marshal:"-"` // 未读纯文本 (UNREAD_TEXT = 1)
	UnreadItem  int     `marshal:"-"` // 未读有附件 (UNREAD_ITEM = 2)
	UnreadTrade int     `marshal:"-"` // 未读交易 (UNREAD_TRADE = 3)
	ReadText    int     `marshal:"-"` // 已读纯文本 (READ_TEXT = 4)
	ReadItem    int     `marshal:"-"` // 已读有附件 (READ_ITEM = 5)
	ReadTrade   int     `marshal:"-"` // 已读交易 (READ_TRADE = 6)
	ReadNoItem  int     `marshal:"-"` // 已读无附件 (READ_NO_ITEM = 7)
	sortedMails []*Mail `marshal:"-"` // 合并排序后的缓存（仅masterType使用）
}

// GetUnreadTotal 获取未读总数
func (c *MailTypeCache) GetUnreadTotal() int {
	return c.UnreadText + c.UnreadItem + c.UnreadTrade
}

// GetReadTotal 获取已读总数
func (c *MailTypeCache) GetReadTotal() int {
	return c.ReadText + c.ReadItem + c.ReadTrade + c.ReadNoItem
}

// GetTotal 获取总数
func (c *MailTypeCache) GetTotal() int {
	return c.GetUnreadTotal() + c.GetReadTotal()
}

// IsEmpty 检查是否为空
func (c *MailTypeCache) IsEmpty() bool {
	return c.GetTotal() == 0
}

// GetStatusCount 根据状态获取数量
func (c *MailTypeCache) GetStatusCount(status MailDefine.MAIL_STATUS) int {
	switch status {
	case MailDefine.UNREAD_TEXT:
		return c.UnreadText
	case MailDefine.UNREAD_ITEM:
		return c.UnreadItem
	case MailDefine.UNREAD_TRADE:
		return c.UnreadTrade
	case MailDefine.READ_TEXT:
		return c.ReadText
	case MailDefine.READ_ITEM:
		return c.ReadItem
	case MailDefine.READ_TRADE:
		return c.ReadTrade
	case MailDefine.READ_NO_ITEM:
		return c.ReadNoItem
	default:
		return 0
	}
}

// SetStatusCount 设置状态数量
func (c *MailTypeCache) SetStatusCount(status MailDefine.MAIL_STATUS, count int) {
	if count < 0 {
		count = 0
	}
	switch status {
	case MailDefine.UNREAD_TEXT:
		c.UnreadText = count
	case MailDefine.UNREAD_ITEM:
		c.UnreadItem = count
	case MailDefine.UNREAD_TRADE:
		c.UnreadTrade = count
	case MailDefine.READ_TEXT:
		c.ReadText = count
	case MailDefine.READ_ITEM:
		c.ReadItem = count
	case MailDefine.READ_TRADE:
		c.ReadTrade = count
	case MailDefine.READ_NO_ITEM:
		c.ReadNoItem = count
	}
}

// AddStatusCount 增加状态数量
func (c *MailTypeCache) AddStatusCount(status MailDefine.MAIL_STATUS, delta int) {
	current := c.GetStatusCount(status)
	c.SetStatusCount(status, current+delta)
}

// HasUnread 检查是否有未读邮件（基于mailsCache计算）
func (m *MailCacheData) HasUnread() bool {
	if m.mailsCache == nil {
		return false
	}
	for _, cache := range m.mailsCache {
		if cache.GetUnreadTotal() > 0 {
			return true
		}
	}
	return false
}

// AddMail 添加邮件 这里没考虑锁 一定要注意上层调用
func (m *MailCacheData) AddMail(mail *Mail) {
	if m.mailsCache == nil {
		m.mailsCache = make(map[MailDefine.MAIL_TYPE]*MailTypeCache)
	}
	// 增加类型-状态数量
	m.ChangeMailStatusCount(mail.typ, MailDefine.STATUS_UNKNOWN, mail.status, 1)
	// 获取或创建该类型的缓存
	cache, exists := m.mailsCache[mail.typ]
	if !exists {
		cache = &MailTypeCache{
			mails:    make([]*Mail, 0),
			lastId:   "",
			lastPage: 0,
		}
		m.mailsCache[mail.typ] = cache
	}

	// 更新邮件缓存（新邮件在前）
	cache.mails = append([]*Mail{mail}, cache.mails...)
	if cache.sortedMails == nil {
		cache.sortedMails = make([]*Mail, 0)
	}
	cache.sortedMails = append([]*Mail{mail}, cache.sortedMails...)
}

// RemoveMail 移除邮件 这里没考虑锁 一定要注意上层调用
func (m *MailCacheData) RemoveMail(mail *Mail) {
	if mail == nil || m.mailsCache == nil {
		return
	}

	cache, exists := m.mailsCache[mail.typ]
	if !exists {
		return
	}

	_, index, _ := lo.FindIndexOf(cache.mails, func(item *Mail) bool { return item == mail })
	if index != -1 {
		m.deleteWaitAry = append(m.deleteWaitAry, mail)
		cache.mails = append(cache.mails[:index], cache.mails[index+1:]...)
		m.ChangeMailStatusCount(mail.typ, mail.status, MailDefine.STATUS_UNKNOWN, 1)
		_, index, _ = lo.FindIndexOf(cache.sortedMails, func(item *Mail) bool { return item == mail })
		if index != -1 {
			cache.sortedMails = append(cache.sortedMails[:index], cache.sortedMails[index+1:]...)
		}
	}
}

func (m *MailCacheData) GetMailById(id int64, typ MailDefine.MAIL_TYPE) *Mail {
	if m.mailsCache == nil {
		return nil
	}
	cache := m.mailsCache[typ]
	if cache == nil {
		return nil
	}
	for _, mail := range cache.mails {
		if mail.id == id {
			return mail
		}
	}
	return nil
}

func (m *MailCacheData) GetNumInfo() map[MailDefine.MAIL_TYPE]*MailTypeStats {
	// 初始化缓存map
	if m.mailsCache == nil {
		m.mailsCache = make(map[MailDefine.MAIL_TYPE]*MailTypeCache)
	}

	// 检查是否需要从数据库加载统计信息
	needLoadFromDB := len(m.mailsCache) == 0

	if needLoadFromDB {
		// 尝试从Redis加载,Redis没有数据，从数据库加载
		if !m.loadFromRedis() {
			m.loadStatsFromDB()
		}
	}

	// 将MailTypeCache转换为MailTypeStats格式返回（为了兼容性）
	result := make(map[MailDefine.MAIL_TYPE]*MailTypeStats)
	for mailType, cache := range m.mailsCache {
		stats := &MailTypeStats{
			UnreadText:  cache.UnreadText,
			UnreadItem:  cache.UnreadItem,
			UnreadTrade: cache.UnreadTrade,
			ReadText:    cache.ReadText,
			ReadItem:    cache.ReadItem,
			ReadTrade:   cache.ReadTrade,
			ReadNoItem:  cache.ReadNoItem,
		}
		result[mailType] = stats
	}

	return result
}

// loadStatsFromDB 从数据库加载统计信息
func (m *MailCacheData) loadStatsFromDB() {
	sTime := time.Now()

	// 使用聚合查询一次性获取所有统计信息
	// 建议创建复合索引：db.mail.createIndex({"receiver": 1, "typ": 1, "status": 1})
	pipeline := mongo.Pipeline{
		// 第一阶段：过滤指定接收者的邮件
		{{Key: "$match", Value: bson.M{"receiver": m.plr.GetGameId()}}},
		// 第二阶段：按邮件类型和状态分组统计
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"typ":    "$typ",
				"status": "$status",
			},
			"count": bson.M{"$sum": 1},
		}}},
	}

	cursor, err := db.MAIL.GetCollection().Aggregate(context.Background(), pipeline)
	if err != nil {
		log.Warning("聚合查询邮件统计失败: %v", err)
		return
	}
	defer cursor.Close(context.Background())

	// 解析聚合结果
	var results []bson.M
	if err = cursor.All(context.Background(), &results); err != nil {
		log.Warning("解析聚合结果失败: %v", err)
		return
	}

	if m.mailsCache == nil {
		m.mailsCache = make(map[MailDefine.MAIL_TYPE]*MailTypeCache)
	}
	// 处理聚合结果
	for _, result := range results {
		// 获取_id字段
		idField, ok := result["_id"].(bson.M)
		if !ok {
			log.Warning("聚合结果_id字段类型错误: %T", result["_id"])
			continue
		}

		// 获取邮件类型ID
		typId, ok := idField["typ"].(int32)
		if !ok {
			log.Warning("邮件类型ID类型错误: %T, 值: %v", idField["typ"], idField["typ"])
			continue
		}

		// 获取状态ID
		statusId, ok := idField["status"].(int32)
		if !ok {
			log.Warning("邮件状态ID类型错误: %T, 值: %v", idField["status"], idField["status"])
			continue
		}

		// 获取数量
		count, ok := result["count"].(int32)
		if !ok {
			log.Warning("邮件数量类型错误: %T, 值: %v", result["count"], result["count"])
			continue
		}

		mailType := MailDefine.MAIL_TYPE(typId)
		status := MailDefine.MAIL_STATUS(statusId)

		// 跳过TOTAL类型和未知状态
		if mailType == MailDefine.TOTAL || status == MailDefine.STATUS_UNKNOWN {
			continue
		}

		// 验证数量的合理性
		if count < 0 {
			log.Warning("邮件数量为负数，玩家ID:%s, 类型:%d, 状态:%d, 数量:%d",
				m.plr.GetGameId(), mailType, status, count)
			continue
		}

		// 确保cache对象存在
		cache := m.mailsCache[mailType]
		if cache == nil {
			cache = &MailTypeCache{
				mails:    make([]*Mail, 0),
				lastId:   "",
				lastPage: 0,
			}
			m.mailsCache[mailType] = cache
		}

		// 设置对应状态的数量
		cache.SetStatusCount(status, int(count))
	}

	// 保存到Redis缓存
	m.saveToRedis()

	log.Debug("loadStatsFromDB 完成，类型数量:%d, 耗时:%.3fs", len(m.mailsCache), time.Since(sTime).Seconds())
}

// save 批量保存邮件数据到数据库
func (m *MailCacheData) save() {
	if len(m.mailsCache) == 0 {
		return
	}
	// 收集需要更新/删除的邮件
	needUpdateMails := make([]*Mail, 0)
	for _, typ := range m.mailsCache {
		for _, mail := range typ.mails {
			if mail.update {
				needUpdateMails = append(needUpdateMails, mail)
			}
		}
	}

	if len(needUpdateMails) == 0 && len(m.deleteWaitAry) == 0 {
		return
	}
	unique := m.plr.GetId()
	tm := ut.LPLock(unique)
	defer ut.LPUnlock(tm)

	// 批量大小设置为50，基于邮件数据大小分析
	// 单个邮件估算2-10KB，50个邮件约100-500KB，安全范围内
	const batchSize = 50
	sTime := time.Now()
	totalUpdated := 0
	// 分批处理
	for i := 0; i < len(needUpdateMails); i += batchSize {
		end := i + batchSize
		if end > len(needUpdateMails) {
			end = len(needUpdateMails)
		}

		batch := needUpdateMails[i:end]
		if err := m.saveBatch(batch); err != nil {
			log.Error("批量保存邮件失败，玩家ID:%s, 批次:%d-%d, 错误:%v",
				m.plr.GetGameId(), i, end-1, err)
			continue
		}

		// 清除update标记
		for _, mail := range batch {
			mail.update = false
		}
		totalUpdated += len(batch)
	}
	log.Debug("邮件批量保存完成，玩家ID:%s, 更新数量:%d, 耗时:%.3fs",
		m.plr.GetGameId(), totalUpdated, time.Since(sTime).Seconds())
	// 批量删除
	for i := 0; i < len(m.deleteWaitAry); i += batchSize {
		end := i + batchSize
		if end > len(m.deleteWaitAry) {
			end = len(m.deleteWaitAry)
		}
		batch := m.deleteWaitAry[i:end]
		if err := m.deleteBatch(batch); err != nil {
			log.Error("批量删除邮件失败，玩家ID:%s, 批次:%d-%d, 错误:%v",
				m.plr.GetGameId(), i, end-1, err)
			continue
		}
		totalUpdated += len(batch)
	}
}

// saveBatch 批量保存一批邮件到数据库
func (m *MailCacheData) saveBatch(mails []*Mail) error {
	if len(mails) == 0 {
		return nil
	}
	operations := make([]mongo.WriteModel, 0, len(mails))
	for _, mail := range mails {
		filter := bson.M{"id": mail.id}
		doc := serializer.ToBsonM(mail)
		// 处理插入和更新
		operation := mongo.NewReplaceOneModel()
		operation.SetFilter(filter)
		operation.SetReplacement(doc)
		operation.SetUpsert(true)
		operations = append(operations, operation)
	}
	opts := options.BulkWrite().SetOrdered(false)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	_, err := db.MAIL.GetCollection().BulkWrite(ctx, operations, opts)
	return err
}

func (m *MailCacheData) deleteBatch(mails []*Mail) error {
	if len(mails) == 0 {
		return nil
	}
	operations := make([]mongo.WriteModel, 0, len(mails))
	for _, mail := range mails {
		filter := bson.M{"id": mail.id}
		operation := mongo.NewDeleteOneModel()
		operation.SetFilter(filter)
		operations = append(operations, operation)
	}
	opts := options.BulkWrite().SetOrdered(false)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	_, err := db.MAIL.GetCollection().BulkWrite(ctx, operations, opts)
	return err
}

// Redis缓存相关方法

// loadFromRedis 从Redis加载邮件缓存数据
func (m *MailCacheData) loadFromRedis() bool {
	if m.plr == nil {
		return false
	}

	redisKey := db.RedisKeyMailCache(m.plr.GetGameId())
	ctx := context.Background()

	// 所有可能的状态（除了STATUS_UNKNOWN）
	statuses := []MailDefine.MAIL_STATUS{
		MailDefine.UNREAD_TEXT, MailDefine.UNREAD_ITEM, MailDefine.UNREAD_TRADE,
		MailDefine.READ_TEXT, MailDefine.READ_ITEM, MailDefine.READ_TRADE, MailDefine.READ_NO_ITEM,
	}

	// 构建要获取的字段列表
	fields := make([]string, 0, len(AllMailTypes)*len(statuses))
	for _, mailType := range AllMailTypes {
		for _, status := range statuses {
			fields = append(fields, fmt.Sprintf("type_%d_status_%d", mailType, status))
		}
	}

	// 使用HMGET一次性获取所有字段
	result := db.GetRedis().HMGet(ctx, redisKey, fields...)
	if result.Err() != nil {
		log.Warning("从Redis加载邮件缓存失败，玩家ID:%s, 错误:%v", m.plr.GetGameId(), result.Err())
		return false
	}

	values := result.Val()
	if len(values) == 0 {
		return false
	}
	if len(lo.Filter(values, func(v interface{}, i int) bool { return v != nil })) == 0 {
		return false
	}

	// 初始化缓存map
	if m.mailsCache == nil {
		m.mailsCache = make(map[MailDefine.MAIL_TYPE]*MailTypeCache)
		for _, mailType := range AllMailTypes {
			m.mailsCache[mailType] = &MailTypeCache{
				mails:    make([]*Mail, 0),
				lastId:   "",
				lastPage: 0,
			}
		}
	}

	// 解析数据
	for i, mailType := range AllMailTypes {
		cache := &MailTypeCache{
			mails:    make([]*Mail, 0),
			lastId:   "",
			lastPage: 0,
		}
		hasTypeData := false

		for j, status := range statuses {
			idx := i*len(statuses) + j
			if idx >= len(values) || values[idx] == nil {
				continue
			}

			if countStr, ok := values[idx].(string); ok {
				if count, err := strconv.Atoi(countStr); err == nil && count > 0 {
					cache.SetStatusCount(status, count)
					hasTypeData = true
				}
			}
		}

		// 只有当该类型有数据时才添加到map中
		if hasTypeData {
			m.mailsCache[mailType] = cache
		}
	}

	return len(m.mailsCache) > 0
}

// saveToRedis 保存邮件缓存数据到Redis
func (m *MailCacheData) saveToRedis() error {
	if m.plr == nil || m.mailsCache == nil {
		return nil
	}

	redisKey := db.RedisKeyMailCache(m.plr.GetGameId())
	ctx := context.Background()

	// 构建要设置的字段
	fields := make([]interface{}, 0)

	// 所有可能的状态（除了STATUS_UNKNOWN）
	statuses := []MailDefine.MAIL_STATUS{
		MailDefine.UNREAD_TEXT, MailDefine.UNREAD_ITEM, MailDefine.UNREAD_TRADE,
		MailDefine.READ_TEXT, MailDefine.READ_ITEM, MailDefine.READ_TRADE, MailDefine.READ_NO_ITEM,
	}

	for mailType, cache := range m.mailsCache {
		for _, status := range statuses {
			count := cache.GetStatusCount(status)
			field := fmt.Sprintf("type_%d_status_%d", mailType, status)
			fields = append(fields, field, fmt.Sprintf("%d", count))
		}
	}

	if len(fields) == 0 {
		return nil
	}

	// 使用HMSET设置所有字段
	result := db.GetRedis().HMSet(ctx, redisKey, fields...)
	if result.Err() != nil {
		log.Warning("保存邮件缓存到Redis失败，玩家ID:%s, 错误:%v", m.plr.GetGameId(), result.Err())
		return result.Err()
	}

	log.Debug("保存邮件缓存到Redis成功，玩家ID:%s, 字段数量:%d", m.plr.GetGameId(), len(fields)/2)
	return nil
}

// ChangeMailStatusCount 改变邮件状态数量
//
// Parameters:
//   - mailType MailDefine.MAIL_TYPE 邮件类型
//   - before MailDefine.MAIL_STATUS 改变前的状态
//   - after MailDefine.MAIL_STATUS 改变后的状态
//   - cnt int 改变的数量 相对于before是减去，相对于after是加上
func (m *MailCacheData) ChangeMailStatusCount(mailType MailDefine.MAIL_TYPE, before, after MailDefine.MAIL_STATUS, cnt int) {
	if m.mailsCache == nil {
		m.mailsCache = make(map[MailDefine.MAIL_TYPE]*MailTypeCache)
	}

	cache := m.mailsCache[mailType]
	if cache == nil {
		cache = &MailTypeCache{
			mails:    make([]*Mail, 0),
			lastId:   "",
			lastPage: 0,
		}
		m.mailsCache[mailType] = cache
	}
	if before != MailDefine.STATUS_UNKNOWN {
		currentCount := cache.GetStatusCount(before)
		newCount := currentCount - cnt
		cache.SetStatusCount(before, newCount)
	}
	if after != MailDefine.STATUS_UNKNOWN {
		currentCount := cache.GetStatusCount(after)
		newCount := currentCount + cnt
		cache.SetStatusCount(after, newCount)
	}

	// 如果该类型邮件数量为0，从map中删除
	// if cache.IsEmpty() {
	// 	delete(m.mailsCache, mailType)
	// }
	m.saveToRedis()
}

// UpdatePlayerMailStatusCountRedis 更新某个玩家的某个类型邮件的某个状态的数量（直接操作Redis，用于离线玩家）
func UpdatePlayerMailStatusCountRedis(gameId int, mailType MailDefine.MAIL_TYPE, status MailDefine.MAIL_STATUS, delta int) error {
	redisKey := db.RedisKeyMailCache(gameId)
	ctx := context.Background()

	fieldName := fmt.Sprintf("type_%d_status_%d", mailType, status)

	// 使用Lua脚本保证原子性
	script := `
		local key = KEYS[1]
		local field = ARGV[1]
		local delta = tonumber(ARGV[2])

		local current = redis.call("HGET", key, field)
		if current == false then
			current = 0
		else
			current = tonumber(current)
		end

		local newValue = current + delta
		if newValue < 0 then
			newValue = 0
		end

		if newValue == 0 then
			redis.call("HDEL", key, field)
		else
			redis.call("HSET", key, field, newValue)
		end

		return newValue
	`

	result := db.GetRedis().Eval(ctx, script, []string{redisKey}, fieldName, fmt.Sprintf("%d", delta))
	if result.Err() != nil {
		log.Warning("更新玩家邮件状态计数失败，玩家ID:%s, 类型:%d, 状态:%d, 错误:%v", gameId, mailType, status, result.Err())
		return result.Err()
	}

	log.Debug("更新玩家邮件状态计数成功，玩家ID:%s, 类型:%d, 状态:%d, 字段:%s, 变化:%d",
		gameId, mailType, status, fieldName, delta)
	return nil
}

// GetMailList 获取指定类型的邮件列表（分页）
func (m *MailCacheData) GetMailList(mailType MailDefine.MAIL_TYPE, page int) ([]*Mail, int, error) {
	if m.plr == nil {
		return nil, 0, fmt.Errorf("玩家为空")
	}

	// 每页固定50条
	const pageSize = 50

	// 初始化缓存map
	if m.mailsCache == nil {
		m.mailsCache = make(map[MailDefine.MAIL_TYPE]*MailTypeCache)
	}

	// 获取该类型的缓存，如果不存在则先加载统计信息
	cache, exists := m.mailsCache[mailType]
	if !exists {
		// 先加载统计信息
		m.GetNumInfo()
		cache, exists = m.mailsCache[mailType]
		if !exists || cache.IsEmpty() {
			// 该类型没有邮件
			return []*Mail{}, 0, nil
		}
	}

	masterType := mailType
	searchTypes := make([]MailDefine.MAIL_TYPE, 0)
	searchTypes = append(searchTypes, mailType)

	// 计算总数量和总页数
	totalCount := cache.GetTotal()
	canMerge, ok := CanMergeMailType[mailType]
	if ok {
		cache2 := m.mailsCache[canMerge]
		if cache2 != nil {
			totalCount += cache2.GetTotal()
		}
		searchTypes = append(searchTypes, canMerge)

		// 设计的以小的类型为masterType，这样可以保证lastId在查询时不会漏掉符合条件的邮件
		if int32(canMerge) < int32(masterType) {
			masterType = canMerge
			cache = cache2
		}
	}

	totalPages := (totalCount + pageSize - 1) / pageSize

	// 检查页码有效性
	if page < 1 || page > totalPages {
		return []*Mail{}, totalPages, nil
	}

	// 检查是否需要从数据库查询
	needQuery := false
	if cache.lastPage < page {
		needQuery = true
	}

	// 如果需要查询，从数据库获取数据
	if needQuery {
		newMails, err := m.queryMailsFromDBWithCursor(masterType, cache.lastId, (page-cache.lastPage)*pageSize, searchTypes...)
		if err != nil {
			log.Error("查询邮件失败，玩家ID:%s, 主类型:%d, 查询类型:%v,页码:%d, 错误:%v",
				m.plr.GetGameId(), masterType, searchTypes, page, err)
			return nil, totalCount, err
		}
		// 将新查询的邮件追加到各自的缓存
		for _, mail := range newMails {
			m.mailsCache[mail.GetTyp()].mails = append(m.mailsCache[mail.GetTyp()].mails, mail)
		}
		// 放到有序的缓存中去
		if cache.sortedMails == nil {
			cache.sortedMails = make([]*Mail, 0)
		}
		cache.sortedMails = append(cache.sortedMails, newMails...)
		cache.lastPage = page
		if len(newMails) > 0 {
			// 更新lastId为最后一条记录的MongoDB _id
			cache.lastId = newMails[len(newMails)-1].GetMongoId()
		}
	}

	// 从合并后的邮件数据中获取
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize
	if endIndex > len(cache.sortedMails) {
		endIndex = len(cache.sortedMails)
	}

	if startIndex >= len(cache.sortedMails) {
		return []*Mail{}, totalCount, nil
	}

	result := cache.sortedMails[startIndex:endIndex]
	return result, totalCount, nil
}

// queryMailsFromDBWithCursor 使用游标方式从数据库查询邮件
//
// Parameters:
//   - lastId string
//   - limit int
//   - masterType MailDefine.MAIL_TYPE lastId存放在哪个类型  也就是说哪个类型是主要类型
//   - searchTypes ...MailDefine.MAIL_TYPE 可以合并的邮件查询类型
//
// Returns:
//   - []*Mail
//   - error
func (m *MailCacheData) queryMailsFromDBWithCursor(masterType MailDefine.MAIL_TYPE, lastId string, limit int, searchTypes ...MailDefine.MAIL_TYPE) ([]*Mail, error) {
	sTime := time.Now()

	// 构建查询条件
	filter := bson.M{
		"receiver": m.plr.GetGameId(),
	}
	if len(searchTypes) == 1 {
		filter["typ"] = searchTypes[0]
	} else {
		filter["typ"] = bson.M{"$in": searchTypes}
	}

	// 如果有lastId，添加_id范围条件（_id < lastId，因为_id是按时间递增的，我们要查询更早的邮件）
	if lastId != "" {
		// 将string类型的lastId转换为ObjectId
		if oid, err := primitive.ObjectIDFromHex(lastId); err == nil {
			filter["_id"] = bson.M{"$lt": oid}
		}
	}

	// 设置查询选项：按_id降序排序（新邮件在前），限制数量
	opts := options.Find().
		SetSort(bson.M{"_id": -1}).
		SetLimit(int64(limit))

	// 执行查询
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cursor, err := db.MAIL.GetCollection().Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("数据库查询失败: %v", err)
	}
	defer cursor.Close(ctx)

	// 解析结果
	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("解析查询结果失败: %v", err)
	}

	// 转换为Mail对象
	mails := make([]*Mail, 0, len(results))
	for _, result := range results {
		mail := &Mail{}
		// 先将bson.M转换为BSON字节数组，再反序列化
		data, err := bson.Marshal(result)
		if err != nil {
			log.Warning("邮件数据序列化失败，跳过该邮件: %v", err)
			continue
		}
		if err := serializer.UnmarshalBSON(data, mail); err != nil {
			log.Warning("邮件数据反序列化失败，跳过该邮件: %v", err)
			continue
		}

		// 设置_id字段（从MongoDB的_id获取）
		if oid, ok := result["_id"].(primitive.ObjectID); ok {
			mail.SetMongoId(oid.Hex())
		}

		mails = append(mails, mail)
	}

	log.Debug("游标查询邮件完成，玩家ID:%s, 主类型:%d,查询类型:%v, lastId:%s, 数量:%d, 耗时:%.3fs",
		m.plr.GetGameId(), masterType, searchTypes, lastId, len(mails), time.Since(sTime).Seconds())

	return mails, nil
}
